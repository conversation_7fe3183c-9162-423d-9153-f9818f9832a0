#!/usr/bin/env python3
"""
Build script for creating cross-platform TorchSparse wheel packages.

This script automates the process of building wheel packages for different
Python versions, PyTorch versions, and CUDA configurations on Windows and Linux.
The script now dynamically detects available versions and adapts to the environment.
"""

import os
import sys
import subprocess
import platform
import shutil
import re
from pathlib import Path

# Try to import packaging, use fallback if not available
try:
    from packaging import version
except ImportError:
    print("⚠️  packaging module not available, using fallback version comparison")
    # Simple fallback version comparison
    class SimpleVersion:
        def __init__(self, version_str):
            self.version_str = version_str
            # Parse version into tuple of integers for comparison
            parts = version_str.split('.')
            self.parts = []
            for part in parts:
                try:
                    self.parts.append(int(part))
                except ValueError:
                    # Handle non-numeric parts
                    self.parts.append(0)

        def __ge__(self, other):
            return self.parts >= other.parts

        def __le__(self, other):
            return self.parts <= other.parts

        def __gt__(self, other):
            return self.parts > other.parts

        def __lt__(self, other):
            return self.parts < other.parts

        def __eq__(self, other):
            return self.parts == other.parts

    class version:
        @staticmethod
        def parse(version_str):
            return SimpleVersion(version_str)

# Default fallback versions (used when dynamic detection fails)
DEFAULT_PYTHON_VERSIONS = ["3.8", "3.9", "3.10", "3.11", "3.12"]
DEFAULT_CUDA_VERSIONS = ["11.1", "11.3", "11.6", "11.7", "11.8", "12.0", "12.1", "12.4"]

# Minimum supported versions
MIN_PYTHON_VERSION = "3.8"
MIN_PYTORCH_VERSION = "1.9.0"
MIN_CUDA_VERSION = "11.1"

def detect_available_python_versions():
    """Dynamically detect available Python versions on the system."""
    print("🔍 Detecting available Python versions...")
    available_versions = []

    # Common Python executable patterns
    python_patterns = [
        "python{major}.{minor}",
        "python{major}{minor}",
        "py -{major}.{minor}",  # Windows py launcher
    ]

    # Check versions from 3.8 to 3.13 (future-proofing)
    for major in [3]:
        for minor in range(8, 14):
            version_str = f"{major}.{minor}"

            for pattern in python_patterns:
                cmd = pattern.format(major=major, minor=minor)
                try:
                    result = subprocess.run([cmd, "--version"],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and version_str in result.stdout:
                        if version_str not in available_versions:
                            available_versions.append(version_str)
                            print(f"  ✅ Found Python {version_str}")
                        break
                except (FileNotFoundError, subprocess.TimeoutExpired):
                    continue

    # Also check generic python/python3 commands
    for cmd in ["python", "python3"]:
        try:
            result = subprocess.run([cmd, "--version"],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version_match = re.search(r'Python (\d+\.\d+)', result.stdout)
                if version_match:
                    version_str = version_match.group(1)
                    if (version.parse(version_str) >= version.parse(MIN_PYTHON_VERSION) and
                        version_str not in available_versions):
                        available_versions.append(version_str)
                        print(f"  ✅ Found Python {version_str} (via {cmd})")
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

    # Sort versions
    available_versions.sort(key=lambda x: version.parse(x))

    if not available_versions:
        print("  ⚠️  No Python versions detected, using defaults")
        return DEFAULT_PYTHON_VERSIONS

    print(f"  📋 Available Python versions: {', '.join(available_versions)}")
    return available_versions

def detect_available_cuda_versions():
    """Dynamically detect available CUDA versions."""
    print("🔍 Detecting available CUDA versions...")
    available_versions = []

    # Try to get CUDA version from nvcc
    try:
        result = subprocess.run(["nvcc", "--version"],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            # Parse CUDA version from nvcc output
            version_match = re.search(r'release (\d+\.\d+)', result.stdout)
            if version_match:
                cuda_version = version_match.group(1)
                if version.parse(cuda_version) >= version.parse(MIN_CUDA_VERSION):
                    available_versions.append(cuda_version)
                    print(f"  ✅ Found CUDA {cuda_version} (via nvcc)")
    except (FileNotFoundError, subprocess.TimeoutExpired):
        print("  ⚠️  nvcc not found")

    # Try to detect CUDA from PyTorch if available
    try:
        import torch
        if torch.cuda.is_available():
            torch_cuda_version = torch.version.cuda
            if torch_cuda_version and torch_cuda_version not in available_versions:
                if version.parse(torch_cuda_version) >= version.parse(MIN_CUDA_VERSION):
                    available_versions.append(torch_cuda_version)
                    print(f"  ✅ Found CUDA {torch_cuda_version} (via PyTorch)")
    except ImportError:
        pass

    # Check common CUDA installation paths
    cuda_paths = []
    if platform.system() == "Windows":
        cuda_paths = [
            "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA",
            "C:/Program Files (x86)/NVIDIA GPU Computing Toolkit/CUDA"
        ]
    else:
        cuda_paths = [
            "/usr/local/cuda",
            "/opt/cuda",
            "/usr/cuda"
        ]

    for base_path in cuda_paths:
        if Path(base_path).exists():
            for item in Path(base_path).iterdir():
                if item.is_dir():
                    version_match = re.search(r'v?(\d+\.\d+)', item.name)
                    if version_match:
                        cuda_version = version_match.group(1)
                        if (version.parse(cuda_version) >= version.parse(MIN_CUDA_VERSION) and
                            cuda_version not in available_versions):
                            available_versions.append(cuda_version)
                            print(f"  ✅ Found CUDA {cuda_version} (via filesystem)")

    # Sort versions
    available_versions.sort(key=lambda x: version.parse(x))

    if not available_versions:
        print("  ⚠️  No CUDA versions detected, using defaults")
        return DEFAULT_CUDA_VERSIONS

    print(f"  📋 Available CUDA versions: {', '.join(available_versions)}")
    return available_versions

def get_cuda_tag_for_version(cuda_version):
    """Get the correct PyTorch CUDA tag for a given CUDA version."""
    cuda_ver = version.parse(cuda_version)

    # Map CUDA versions to their corresponding PyTorch CUDA tags
    if cuda_ver >= version.parse("12.8"):
        return "cu128"
    elif cuda_ver >= version.parse("12.6"):
        return "cu126"
    elif cuda_ver >= version.parse("12.4"):
        return "cu124"
    elif cuda_ver >= version.parse("12.1"):
        return "cu121"
    elif cuda_ver >= version.parse("12.0"):
        return "cu121"  # 12.0 uses cu121 tag
    elif cuda_ver >= version.parse("11.8"):
        return "cu118"
    elif cuda_ver >= version.parse("11.7"):
        return "cu117"
    elif cuda_ver >= version.parse("11.6"):
        return "cu116"
    elif cuda_ver >= version.parse("11.3"):
        return "cu113"
    elif cuda_ver >= version.parse("11.1"):
        return "cu111"
    else:
        # For very old CUDA versions, use cu111 as fallback
        return "cu111"

def detect_latest_pytorch_version_simple(cuda_tag="cpu"):
    """Detect latest PyTorch version by attempting installation without version constraints."""
    print(f"🔍 Detecting latest PyTorch version for {cuda_tag} via pip...")

    try:
        # Create a temporary virtual environment to test installation
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            venv_path = Path(temp_dir) / "test_env"

            # Create virtual environment
            subprocess.run([sys.executable, "-m", "venv", str(venv_path)],
                         check=True, capture_output=True, timeout=30)

            # Get pip executable
            if platform.system() == "Windows":
                pip_exe = venv_path / "Scripts" / "pip.exe"
            else:
                pip_exe = venv_path / "bin" / "pip"

            # Install PyTorch without version constraints
            if cuda_tag == "cpu":
                install_cmd = [str(pip_exe), "install", "torch", "--index-url", "https://download.pytorch.org/whl/cpu"]
            else:
                install_cmd = [str(pip_exe), "install", "torch", "--index-url", f"https://download.pytorch.org/whl/{cuda_tag}"]

            result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=120)

            if result.returncode == 0:
                # Get the installed version
                python_exe = venv_path / ("Scripts/python.exe" if platform.system() == "Windows" else "bin/python")
                version_cmd = [str(python_exe), "-c", "import torch; print(torch.__version__)"]
                version_result = subprocess.run(version_cmd, capture_output=True, text=True, timeout=10)

                if version_result.returncode == 0:
                    detected_version = version_result.stdout.strip()
                    print(f"  ✅ Detected latest PyTorch version: {detected_version}")
                    return detected_version
            else:
                print(f"  ⚠️  Failed to install PyTorch: {result.stderr[:200]}...")

    except Exception as e:
        print(f"  ⚠️  Failed to detect PyTorch version: {e}")

    # Fallback to querying index
    return query_latest_pytorch_version_from_index(cuda_tag)

def query_latest_pytorch_version_from_index(cuda_tag="cpu"):
    """Query PyTorch index to find the latest available version."""
    print(f"🔍 Querying PyTorch index for {cuda_tag}...")

    try:
        import urllib.request
        import re

        # Try PyTorch's official index first
        if cuda_tag == "cpu":
            index_url = "https://download.pytorch.org/whl/cpu"
        else:
            index_url = f"https://download.pytorch.org/whl/{cuda_tag}"

        # Try to get the simple index page
        try:
            req = urllib.request.Request(f"{index_url}/torch/")
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            with urllib.request.urlopen(req, timeout=15) as response:
                content = response.read().decode('utf-8')

                # Extract version numbers from the HTML
                if cuda_tag == "cpu":
                    version_pattern = r'torch-(\d+\.\d+\.\d+)(?:\+cpu)?'
                else:
                    version_pattern = r'torch-(\d+\.\d+\.\d+)\+' + cuda_tag

                versions = re.findall(version_pattern, content)

                if versions:
                    # Sort versions and get the latest
                    sorted_versions = sorted(set(versions), key=lambda x: version.parse(x), reverse=True)
                    latest_base = sorted_versions[0]

                    if cuda_tag == "cpu":
                        latest = f"{latest_base}+cpu"
                    else:
                        latest = f"{latest_base}+{cuda_tag}"

                    print(f"  ✅ Found latest PyTorch version: {latest}")
                    return latest
        except Exception as e:
            print(f"  ⚠️  Failed to query PyTorch index: {e}")

        # Fallback: try to detect via pip show
        print("  🔍 Trying pip to detect latest version...")
        try:
            # Try pip search alternative
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "torch==999.999.999"
            ], capture_output=True, text=True, timeout=30)

            # Parse error message to find available versions
            if "No matching distribution found" in result.stderr:
                # Look for version suggestions in error message
                version_match = re.search(r'from versions: ([^)]+)', result.stderr)
                if version_match:
                    versions_str = version_match.group(1)
                    versions = [v.strip() for v in versions_str.split(',')]
                    if versions:
                        latest_base = versions[-1]  # Usually the last one is the latest
                        if cuda_tag == "cpu":
                            latest = f"{latest_base}+cpu"
                        else:
                            latest = f"{latest_base}+{cuda_tag}"
                        print(f"  ✅ Found latest PyTorch version via pip: {latest}")
                        return latest
        except Exception as e:
            print(f"  ⚠️  Pip detection failed: {e}")

    except Exception as e:
        print(f"  ⚠️  Failed to query PyTorch versions: {e}")

    # Ultimate fallback: return a reasonable default
    if cuda_tag == "cpu":
        return "2.5.0+cpu"
    else:
        return f"2.5.0+{cuda_tag}"

def query_latest_pytorch_version(cuda_tag="cpu"):
    """Main function to query latest PyTorch version with multiple strategies."""
    # Try the simple detection first (faster)
    try:
        return detect_latest_pytorch_version_simple(cuda_tag)
    except Exception:
        # Fall back to index querying
        return query_latest_pytorch_version_from_index(cuda_tag)

def get_pytorch_compatible_versions(cuda_version):
    """Get compatible PyTorch versions for a given CUDA version."""
    print(f"🔍 Finding compatible PyTorch versions for CUDA {cuda_version}...")

    # Handle CPU-only case
    if cuda_version.lower() == "cpu" or cuda_version == "none":
        print("  📋 Using CPU-only PyTorch")
        latest_version = query_latest_pytorch_version("cpu")
        pytorch_versions = [latest_version]

        # Add some recent CPU versions as fallbacks
        fallback_versions = ["2.5.0+cpu", "2.4.1+cpu", "2.3.1+cpu", "2.2.2+cpu", "2.1.2+cpu"]
        for fallback in fallback_versions:
            if fallback not in pytorch_versions:
                pytorch_versions.append(fallback)

        print(f"  📋 Compatible PyTorch versions: {', '.join(pytorch_versions[:3])}{'...' if len(pytorch_versions) > 3 else ''}")
        return pytorch_versions

    # Get the correct CUDA tag for this CUDA version
    cuda_tag = get_cuda_tag_for_version(cuda_version)
    print(f"  📋 Using CUDA tag: {cuda_tag}")

    # Query for the latest version with this CUDA tag
    latest_version = query_latest_pytorch_version(cuda_tag)

    # Also try to get a few recent versions
    pytorch_versions = [latest_version]

    # Add some fallback versions based on CUDA tag
    try:
        cuda_ver = version.parse(cuda_version)
        if cuda_ver >= version.parse("12.6"):
            fallback_versions = [f"2.5.1+{cuda_tag}", f"2.5.0+{cuda_tag}", f"2.4.1+{cuda_tag}"]
        elif cuda_ver >= version.parse("12.4"):
            fallback_versions = [f"2.4.1+{cuda_tag}", f"2.4.0+{cuda_tag}", f"2.3.1+{cuda_tag}"]
        elif cuda_ver >= version.parse("12.1"):
            fallback_versions = [f"2.3.1+{cuda_tag}", f"2.3.0+{cuda_tag}", f"2.2.2+{cuda_tag}"]
        elif cuda_ver >= version.parse("11.8"):
            fallback_versions = [f"2.1.2+{cuda_tag}", f"2.1.1+{cuda_tag}", f"2.0.1+{cuda_tag}"]
        else:
            fallback_versions = [f"1.13.1+{cuda_tag}", f"1.12.1+{cuda_tag}", f"1.11.0+{cuda_tag}"]
    except Exception:
        # Fallback if version parsing fails
        fallback_versions = [f"2.5.0+{cuda_tag}", f"2.4.0+{cuda_tag}", f"2.3.0+{cuda_tag}"]

    # Add fallback versions if they're not already in the list
    for fallback in fallback_versions:
        if fallback not in pytorch_versions:
            pytorch_versions.append(fallback)

    # Also add CPU version as ultimate fallback
    cpu_version = latest_version.split('+')[0] + "+cpu"
    if cpu_version not in pytorch_versions:
        pytorch_versions.append(cpu_version)

    print(f"  📋 Compatible PyTorch versions: {', '.join(pytorch_versions[:3])}{'...' if len(pytorch_versions) > 3 else ''}")
    return pytorch_versions

def get_pytorch_index_url(cuda_version, pytorch_version):
    """Get the appropriate PyTorch index URL for the given CUDA and PyTorch versions."""
    if "+cpu" in pytorch_version:
        return "https://download.pytorch.org/whl/cpu"

    # Extract CUDA tag from PyTorch version
    if "+" in pytorch_version:
        cuda_tag = pytorch_version.split("+")[1]
    else:
        # Fallback: determine CUDA tag from CUDA version using the correct mapping
        cuda_tag = get_cuda_tag_for_version(cuda_version)

    return f"https://download.pytorch.org/whl/{cuda_tag}"

def check_prerequisites():
    """Check if all prerequisites are installed (with flexible warnings instead of hard failures)."""
    print("🔍 Checking prerequisites...")

    current_os = platform.system()
    print(f"Operating System: {current_os}")

    if current_os == "Windows":
        return check_windows_prerequisites()
    elif current_os == "Linux":
        return check_linux_prerequisites()
    else:
        print(f"⚠️  Unsupported operating system: {current_os}")
        print("This script is optimized for Windows and Linux, but will attempt to continue.")
        print("You may need to install dependencies manually.")
        return check_common_prerequisites()  # Try common checks anyway

def check_windows_prerequisites():
    """Check Windows-specific prerequisites (with flexible warnings)."""
    print("Checking Windows prerequisites...")

    # Check for Visual Studio (more comprehensive search)
    vs_paths = [
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019",
        r"C:\Program Files\Microsoft Visual Studio\2019",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2022",
        r"C:\Program Files\Microsoft Visual Studio\2022",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2017",
        r"C:\Program Files\Microsoft Visual Studio\2017",
        r"C:\BuildTools",  # Common build tools location
    ]

    vs_found = any(Path(path).exists() for path in vs_paths)

    # Also check for MSVC environment variables
    if not vs_found:
        msvc_vars = ["VCINSTALLDIR", "VCToolsInstallDir", "WindowsSdkDir"]
        vs_found = any(os.environ.get(var) for var in msvc_vars)

    if not vs_found:
        print("⚠️  Visual Studio or Build Tools not detected.")
        print("   This may cause build failures. Consider installing:")
        print("   - Visual Studio 2019/2022 with C++ support")
        print("   - Visual Studio Build Tools")
        print("   - Or ensure MSVC environment is properly set up")
        print("   Continuing anyway...")
    else:
        print("✅ Visual Studio/Build Tools found")

    return check_common_prerequisites()

def check_linux_prerequisites():
    """Check Linux-specific prerequisites (with flexible warnings)."""
    print("Checking Linux prerequisites...")

    # Check for GCC or other compilers
    compilers_found = []
    for compiler in ["gcc", "clang", "cc"]:
        try:
            result = subprocess.run([compiler, "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                compiler_info = result.stdout.split('\n')[0]
                compilers_found.append(f"{compiler}: {compiler_info}")
                print(f"✅ {compiler.upper()} found: {compiler_info}")
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

    if not compilers_found:
        print("⚠️  No C/C++ compiler found.")
        print("   Consider installing: build-essential (Ubuntu/Debian) or gcc (other distros)")
        print("   Continuing anyway...")

    # Check for development headers (more comprehensive)
    python_dev_paths = []
    for major in [3]:
        for minor in range(8, 14):  # Python 3.8 to 3.13
            python_dev_paths.extend([
                f"/usr/include/python{major}.{minor}",
                f"/usr/include/python{major}.{minor}m",
                f"/usr/local/include/python{major}.{minor}",
                f"/opt/python{major}.{minor}/include/python{major}.{minor}",
            ])

    python_dev_found = any(Path(path).exists() for path in python_dev_paths)
    if not python_dev_found:
        print("⚠️  Python development headers may be missing.")
        print("   Consider installing: python3-dev (Ubuntu/Debian) or python3-devel (RHEL/CentOS)")
        print("   Continuing anyway...")
    else:
        print("✅ Python development headers found")

    return check_common_prerequisites()

def check_common_prerequisites():
    """Check prerequisites common to both platforms (with flexible warnings)."""
    # Check for CUDA (optional)
    cuda_found = False
    try:
        result = subprocess.run(["nvcc", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            cuda_info = result.stdout.split('release')[1].split(',')[0].strip()
            print(f"✅ CUDA found: {cuda_info}")
            cuda_found = True
    except (FileNotFoundError, subprocess.TimeoutExpired, IndexError):
        pass

    if not cuda_found:
        print("⚠️  CUDA toolkit not found via nvcc.")
        print("   This is OK if you want CPU-only builds.")
        print("   For GPU support, install CUDA toolkit from:")
        print("   https://developer.nvidia.com/cuda-downloads")

        # Try to detect CUDA via other means
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✅ CUDA detected via PyTorch: {torch.version.cuda}")
                cuda_found = True
        except ImportError:
            pass

    # Check for Python
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            python_version = result.stdout.strip()
            print(f"✅ Python found: {python_version}")
        else:
            print("⚠️  Python version check failed")
    except (FileNotFoundError, subprocess.TimeoutExpired):
        print("⚠️  Python executable not found")

    print("✅ Prerequisites check completed!")
    print("   Note: Some warnings above are OK and won't prevent building.")
    return True  # Always return True to allow flexible building

def setup_sparsehash():
    """Download and setup sparsehash headers."""
    print("📦 Setting up sparsehash...")

    current_os = platform.system()

    if current_os == "Windows":
        return setup_sparsehash_windows()
    elif current_os == "Linux":
        return setup_sparsehash_linux()
    else:
        print(f"❌ Unsupported OS for sparsehash setup: {current_os}")
        return False

def setup_sparsehash_windows():
    """Setup sparsehash on Windows."""
    sparsehash_dir = Path("C:/sparsehash")
    if sparsehash_dir.exists():
        print("✅ Sparsehash already installed.")
        return True

    try:
        import urllib.request
        import zipfile

        url = "https://github.com/sparsehash/sparsehash/archive/refs/tags/sparsehash-2.0.4.zip"
        zip_path = "sparsehash.zip"

        print("📥 Downloading sparsehash...")
        urllib.request.urlretrieve(url, zip_path)

        print("📂 Extracting sparsehash...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall("C:/")

        extracted_dir = Path("C:/sparsehash-sparsehash-2.0.4")
        if extracted_dir.exists():
            extracted_dir.rename(sparsehash_dir)

        os.remove(zip_path)
        print("✅ Sparsehash setup complete!")
        return True

    except Exception as e:
        print(f"❌ Failed to setup sparsehash: {e}")
        return False

def setup_sparsehash_linux():
    """Setup sparsehash on Linux."""
    # Check if sparsehash is available via package manager
    try:
        # Try to find sparsehash in system
        result = subprocess.run(["pkg-config", "--exists", "libsparsehash"],
                              capture_output=True)
        if result.returncode == 0:
            print("✅ Sparsehash found via pkg-config")
            return True
    except FileNotFoundError:
        pass

    # Check common installation paths
    common_paths = [
        "/usr/include/sparsehash",
        "/usr/local/include/sparsehash",
        "/usr/include/google/sparse_hash_map",
        "/usr/local/include/google/sparse_hash_map"
    ]

    if any(Path(path).exists() for path in common_paths):
        print("✅ Sparsehash found in system paths")
        return True

    # Try to install via package manager
    print("📦 Attempting to install sparsehash via package manager...")

    # Try different package managers with and without sudo
    package_managers = [
        (["apt-get", "update"], ["apt-get", "install", "-y", "libsparsehash-dev"]),
        (["yum", "update"], ["yum", "install", "-y", "sparsehash-devel"]),
        (["dnf", "update"], ["dnf", "install", "-y", "sparsehash-devel"]),
        (["pacman", "-Sy"], ["pacman", "-S", "--noconfirm", "sparsehash"])
    ]

    for update_cmd, install_cmd in package_managers:
        try:
            print(f"Trying {install_cmd[0]} with sudo...")
            # Try with sudo first
            subprocess.run(["sudo"] + update_cmd, check=True, capture_output=True)
            subprocess.run(["sudo"] + install_cmd, check=True, capture_output=True)
            print(f"✅ Sparsehash installed via sudo {install_cmd[0]}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                print(f"Trying {install_cmd[0]} without sudo...")
                # Try without sudo as fallback
                subprocess.run(update_cmd, check=True, capture_output=True)
                subprocess.run(install_cmd, check=True, capture_output=True)
                print(f"✅ Sparsehash installed via {install_cmd[0]} (no sudo)")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue

    # If package manager fails, build from source
    print("📦 Building sparsehash from source...")
    return build_sparsehash_from_source()

def build_sparsehash_from_source():
    """Build sparsehash from source on Linux."""
    try:
        import urllib.request
        import tarfile

        url = "https://github.com/sparsehash/sparsehash/archive/refs/tags/sparsehash-2.0.4.tar.gz"
        tar_path = "sparsehash.tar.gz"

        print("📥 Downloading sparsehash source...")
        urllib.request.urlretrieve(url, tar_path)

        print("📂 Extracting sparsehash...")
        with tarfile.open(tar_path, 'r:gz') as tar_ref:
            tar_ref.extractall()

        # Build and install
        build_dir = Path("sparsehash-sparsehash-2.0.4")
        if build_dir.exists():
            os.chdir(build_dir)

            # Configure, build, and install
            subprocess.run(["./configure", "--prefix=/usr/local"], check=True)
            subprocess.run(["make"], check=True)

            # Try to install with sudo, fallback to user install if sudo fails
            try:
                subprocess.run(["sudo", "make", "install"], check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("⚠️  sudo not available or failed, trying user install...")
                subprocess.run(["make", "install", f"PREFIX={os.path.expanduser('~/.local')}"], check=True)
                # Add to environment for subsequent builds
                local_include = os.path.expanduser("~/.local/include")
                current_cppflags = os.environ.get("CPPFLAGS", "")
                os.environ["CPPFLAGS"] = f"{current_cppflags} -I{local_include}"

            os.chdir("..")
            shutil.rmtree(build_dir)

        os.remove(tar_path)
        print("✅ Sparsehash built and installed from source!")
        return True

    except Exception as e:
        print(f"❌ Failed to build sparsehash from source: {e}")
        print("Please install sparsehash manually:")
        print("  Ubuntu/Debian: sudo apt-get install libsparsehash-dev")
        print("  CentOS/RHEL: sudo yum install sparsehash-devel")
        print("  Fedora: sudo dnf install sparsehash-devel")
        print("  Arch: sudo pacman -S sparsehash")
        return False

def setup_environment():
    """Setup environment variables for building."""
    print("🔧 Setting up build environment...")

    current_os = platform.system()

    if current_os == "Windows":
        setup_windows_environment()
    elif current_os == "Linux":
        setup_linux_environment()

    # Common environment variables
    os.environ["FORCE_CUDA"] = "1"
    print("✅ Environment setup complete!")

def setup_windows_environment():
    """Setup Windows-specific environment variables."""
    # Set sparsehash include path
    current_include = os.environ.get("INCLUDE", "")
    sparsehash_include = "C:\\sparsehash\\src"

    if sparsehash_include not in current_include:
        os.environ["INCLUDE"] = f"{current_include};{sparsehash_include}"

    # Set build flags for memory optimization
    os.environ["CL"] = "/O1 /MP4"  # Reduced optimization, parallel compilation
    os.environ["DISTUTILS_USE_SDK"] = "1"
    os.environ["MSSdk"] = "1"

def setup_linux_environment():
    """Setup Linux-specific environment variables."""
    # Set compiler flags for optimization
    current_cxxflags = os.environ.get("CXXFLAGS", "")
    current_cflags = os.environ.get("CFLAGS", "")

    # Add optimization and parallel compilation flags
    os.environ["CXXFLAGS"] = f"{current_cxxflags} -O2 -fopenmp"
    os.environ["CFLAGS"] = f"{current_cflags} -O2"

    # Set number of parallel jobs based on CPU count
    import multiprocessing
    num_jobs = min(multiprocessing.cpu_count(), 8)  # Limit to 8 to avoid memory issues
    os.environ["MAX_JOBS"] = str(num_jobs)

def build_wheel(python_version, cuda_version, torch_version=None):
    """Build wheel for specific Python, CUDA, and PyTorch version."""
    if torch_version is None:
        # Get compatible PyTorch versions and use the first (latest) one
        compatible_versions = get_pytorch_compatible_versions(cuda_version)
        if compatible_versions:
            torch_version = compatible_versions[0]
        else:
            print(f"❌ No compatible PyTorch versions found for CUDA {cuda_version}")
            return False

    print(f"🔨 Building wheel for Python {python_version}, CUDA {cuda_version}, PyTorch {torch_version}...")

    # Create virtual environment name
    cuda_short = cuda_version.replace('.', '')
    torch_short = torch_version.split('+')[0].replace('.', '')
    venv_name = f"build_env_py{python_version.replace('.', '')}_cu{cuda_short}_torch{torch_short}"
    venv_path = Path(venv_name)

    try:
        # Create virtual environment
        python_cmd = get_python_command(python_version)
        if not python_cmd:
            print(f"❌ Python {python_version} not found")
            return False

        subprocess.run([python_cmd, "-m", "venv", str(venv_path)], check=True)

        # Get executables
        if platform.system() == "Windows":
            python_exe = venv_path / "Scripts" / "python.exe"
            pip_exe = venv_path / "Scripts" / "pip.exe"
        else:
            python_exe = venv_path / "bin" / "python"
            pip_exe = venv_path / "bin" / "pip"

        # Install PyTorch with specific CUDA version
        if not install_pytorch(pip_exe, torch_version, cuda_version):
            print(f"❌ Failed to install PyTorch {torch_version}")
            return False

        # Install build dependencies
        subprocess.run([
            str(pip_exe), "install",
            "wheel", "setuptools", "ninja"
        ], check=True)

        # Build wheel
        subprocess.run([
            str(python_exe), "setup.py", "bdist_wheel"
        ], check=True)

        print(f"✅ Wheel built successfully for Python {python_version}, CUDA {cuda_version}, PyTorch {torch_version}")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build wheel: {e}")
        return False

    finally:
        # Cleanup virtual environment
        if venv_path.exists():
            shutil.rmtree(venv_path, ignore_errors=True)

def get_python_command(python_version):
    """Get the appropriate Python command for the version."""
    # Try different Python command formats
    commands = [
        f"python{python_version}",
        f"python{python_version.split('.')[0]}.{python_version.split('.')[1]}",
        f"py -{python_version}",  # Windows py launcher
        "python3",
        "python"
    ]

    for cmd in commands:
        try:
            if cmd.startswith("py -"):
                # Special handling for Windows py launcher
                result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=5)
            else:
                result = subprocess.run([cmd, "--version"], capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and python_version in result.stdout:
                return cmd if not cmd.startswith("py -") else cmd.split()[0] + " " + cmd.split()[1]
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

    # If exact version not found, try to find any compatible version
    print(f"⚠️  Exact Python {python_version} not found, trying fallbacks...")
    for cmd in ["python3", "python"]:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                detected_version = re.search(r'Python (\d+\.\d+)', result.stdout)
                if detected_version:
                    detected = detected_version.group(1)
                    if version.parse(detected) >= version.parse(MIN_PYTHON_VERSION):
                        print(f"   Using Python {detected} instead of {python_version}")
                        return cmd
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

    return None  # No suitable Python found

def install_pytorch(pip_exe, torch_version, cuda_version):
    """Install PyTorch with the specified version and CUDA support."""
    try:
        # Get the appropriate index URL
        index_url = get_pytorch_index_url(cuda_version, torch_version)
        print(f"   Installing PyTorch {torch_version} from {index_url}")

        # Install PyTorch
        install_cmd = [
            str(pip_exe), "install",
            f"torch=={torch_version}",
            "--index-url", index_url
        ]

        result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print("   ⚠️  Failed to install exact version, trying without version constraint...")
            # Try installing without exact version constraint
            base_version = torch_version.split('+')[0]
            install_cmd = [
                str(pip_exe), "install",
                f"torch>={base_version}",
                "--index-url", index_url
            ]
            result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                print("   ⚠️  Failed with index URL, trying default PyPI...")
                # Fallback to default PyPI
                install_cmd = [str(pip_exe), "install", f"torch>={base_version}"]
                result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=300)
                if result.returncode != 0:
                    print(f"   ❌ Failed to install PyTorch: {result.stderr}")
                    return False

        # Install torchvision (let pip figure out the compatible version)
        print("   Installing torchvision...")
        torchvision_cmd = [str(pip_exe), "install", "torchvision", "--index-url", index_url]
        result = subprocess.run(torchvision_cmd, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print("   ⚠️  Failed to install torchvision from index, trying default PyPI...")
            torchvision_cmd = [str(pip_exe), "install", "torchvision"]
            result = subprocess.run(torchvision_cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                print("   ⚠️  Failed to install torchvision, continuing without it...")

        print("   ✅ PyTorch installation completed")
        return True

    except subprocess.TimeoutExpired:
        print("   ❌ PyTorch installation timed out")
        return False
    except Exception as e:
        print(f"   ❌ PyTorch installation failed: {e}")
        return False

def organize_wheels():
    """Organize built wheels into release directory."""
    print("📁 Organizing wheels...")
    
    dist_dir = Path("dist")
    release_dir = Path("release")
    release_dir.mkdir(exist_ok=True)
    
    if not dist_dir.exists():
        print("❌ No dist directory found.")
        return
    
    for wheel_file in dist_dir.glob("*.whl"):
        dest_file = release_dir / wheel_file.name
        shutil.copy2(wheel_file, dest_file)
        print(f"📦 Copied {wheel_file.name}")
    
    print("✅ Wheels organized in release/ directory")

def create_release_notes():
    """Create release notes for the wheel packages."""
    print("📝 Creating release notes...")
    
    release_notes = """# TorchSparse v2.1.0 - Windows Compatible Release

## 🎉 What's New

This release provides native Windows support for TorchSparse with the following improvements:

### ✅ Windows Compatibility Fixes
- **MSVC Compatibility**: Added macros to handle `__asm__` and `__volatile__` keywords
- **Type Safety**: Fixed `long`/`int64_t` type mismatches for Windows
- **Compiler Flags**: Platform-specific compilation flags for optimal Windows builds
- **Dependency Resolution**: Automated sparsehash dependency handling

### 📦 Pre-built Packages

| Python Version | CUDA 11.8 | CUDA 12.1 |
|----------------|------------|------------|
| Python 3.8 | ✅ Available | ✅ Available |
| Python 3.9 | ✅ Available | ✅ Available |
| Python 3.10 | ✅ Available | ✅ Available |
| Python 3.11 | ✅ Available | ✅ Available |

### 🚀 Installation

```bash
# For Python 3.10 with CUDA 11.8
pip install torchsparse-2.1.0-cp310-cp310-win_amd64.whl

# Or install directly from GitHub
pip install git+https://github.com/Deathdadev/torchsparse.git
```

### 🔧 System Requirements

- **OS**: Windows 10/11
- **Python**: 3.8-3.11
- **PyTorch**: 1.9.0+
- **CUDA**: 11.x or 12.x
- **Visual Studio**: 2019 or 2022

### 📋 Compatibility Matrix

All wheels are built with:
- Windows 10/11 x64
- Visual Studio 2019/2022 MSVC compiler
- CUDA 11.8 or 12.1
- Optimized for RTX 20xx/30xx/40xx series GPUs

### 🛠️ Build from Source

See [WINDOWS_SETUP_GUIDE.md](WINDOWS_SETUP_GUIDE.md) for detailed instructions.

### 🐛 Bug Fixes

- Fixed compilation errors with MSVC compiler
- Resolved memory exhaustion during Windows builds
- Fixed sparsehash dependency issues
- Improved error handling for Windows environments

### 🙏 Acknowledgments

Thanks to the original TorchSparse team at MIT-HAN-Lab for the excellent library.
"""
    
    with open("release/RELEASE_NOTES.md", "w") as f:
        f.write(release_notes)
    
    print("✅ Release notes created!")

def main():
    """Main build process."""
    current_os = platform.system()
    print(f"🚀 Starting TorchSparse cross-platform wheel build process on {current_os}...")

    # Detect available versions dynamically
    available_python_versions = detect_available_python_versions()
    available_cuda_versions = detect_available_cuda_versions()

    if not check_prerequisites():
        print("⚠️  Some prerequisites missing, but continuing anyway...")

    if not setup_sparsehash():
        print("⚠️  Sparsehash setup failed, but continuing anyway...")

    setup_environment()

    # Parse command line arguments for selective building
    import argparse
    parser = argparse.ArgumentParser(description="Build TorchSparse wheels")
    parser.add_argument("--python-versions", nargs="+", default=available_python_versions,
                       help="Python versions to build for")
    parser.add_argument("--cuda-versions", nargs="+", default=available_cuda_versions,
                       help="CUDA versions to build for")
    parser.add_argument("--torch-versions", nargs="+", default=None,
                       help="Specific PyTorch versions to build for")
    parser.add_argument("--build-latest-only", action="store_true",
                       help="Build only the latest PyTorch version for each CUDA version")
    parser.add_argument("--max-parallel", type=int, default=1,
                       help="Maximum number of parallel builds")
    parser.add_argument("--use-fallback-versions", action="store_true",
                       help="Use fallback default versions if detection fails")

    args = parser.parse_args()

    # Use fallback versions if requested and detection failed
    if args.use_fallback_versions:
        if not args.python_versions or args.python_versions == available_python_versions:
            args.python_versions = DEFAULT_PYTHON_VERSIONS
        if not args.cuda_versions or args.cuda_versions == available_cuda_versions:
            args.cuda_versions = DEFAULT_CUDA_VERSIONS

    # Build wheels for each combination
    success_count = 0
    total_builds = 0
    failed_builds = []

    for python_version in args.python_versions:
        # More flexible version checking
        if version.parse(python_version) < version.parse(MIN_PYTHON_VERSION):
            print(f"⚠️  Skipping Python {python_version} (below minimum {MIN_PYTHON_VERSION})")
            continue

        for cuda_version in args.cuda_versions:
            if version.parse(cuda_version) < version.parse(MIN_CUDA_VERSION):
                print(f"⚠️  Skipping CUDA {cuda_version} (below minimum {MIN_CUDA_VERSION})")
                continue

            # Get PyTorch versions for this CUDA version
            available_torch_versions = get_pytorch_compatible_versions(cuda_version)
            if not available_torch_versions:
                print(f"⚠️  No PyTorch versions available for CUDA {cuda_version}")
                continue

            # Select which PyTorch versions to build
            if args.build_latest_only:
                torch_versions_to_build = [available_torch_versions[0]]  # Latest only
            elif args.torch_versions:
                # Filter to only requested versions that are available
                torch_versions_to_build = [v for v in args.torch_versions
                                         if v in available_torch_versions]
            else:
                torch_versions_to_build = available_torch_versions[:2]  # Build top 2 versions

            for torch_version in torch_versions_to_build:
                total_builds += 1
                print(f"\n{'='*60}")
                print(f"Building {total_builds}: Python {python_version}, CUDA {cuda_version}, PyTorch {torch_version}")
                print(f"{'='*60}")

                if build_wheel(python_version, cuda_version, torch_version):
                    success_count += 1
                else:
                    failed_builds.append(f"Python {python_version}, CUDA {cuda_version}, PyTorch {torch_version}")

    organize_wheels()
    create_release_notes()

    print("\n🎉 Build process complete!")
    print(f"✅ Successfully built {success_count}/{total_builds} wheels")
    print("📁 Wheels available in: release/")

    if failed_builds:
        print(f"\n❌ Failed builds ({len(failed_builds)}):")
        for failed in failed_builds:
            print(f"  - {failed}")
        print("\nCheck the logs above for error details.")

    # Print build matrix summary
    print("\n📊 Build Matrix Summary:")
    print(f"  Python versions: {', '.join(args.python_versions)}")
    print(f"  CUDA versions: {', '.join(args.cuda_versions)}")
    print(f"  Platform: {current_os}")
    print(f"  Total combinations: {total_builds}")
    print(f"  Success rate: {success_count/total_builds*100:.1f}%" if total_builds > 0 else "  No builds attempted")

if __name__ == "__main__":
    main()
